import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { logout, selectIsAuthenticated, selectUser, selectIsAdmin } from '../store/slices/authSlice';
import '../styles/Header.css';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const isAuthenticated = useSelector(selectIsAuthenticated);
  const user = useSelector(selectUser);
  const isAdmin = useSelector(selectIsAdmin);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const menuItems = [
    { name: 'Home', href: '/' },
    { name: 'Projects', href: '/projects' },
    { name: 'Completed', href: '/completed' },
    { name: 'About', href: '/about' },
    { name: 'Contact', href: '/contact' }
  ];

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  const handleLogout = async () => {
    try {
      await dispatch(logout()).unwrap();
      navigate('/');
      closeMenu();
      setShowUserMenu(false);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const toggleUserMenu = () => {
    setShowUserMenu(!showUserMenu);
  };

  const closeUserMenu = () => {
    setShowUserMenu(false);
  };

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showUserMenu && !event.target.closest('.user-menu')) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showUserMenu]);

  return (
    <motion.header
      className={`header ${isScrolled ? 'header--scrolled' : ''}`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <div className="container">
        <div className="header__content">
          {/* Logo */}
          <motion.div
            className="header__logo"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link to="/" onClick={closeMenu}>
              <span className="logo__text"></span>
              <span className="logo__accent">Anusthan</span>
            </Link>
          </motion.div>

          {/* Desktop Navigation */}
          <nav className="header__nav">
            <ul className="nav__list">
              {menuItems.map((item, index) => (
                <motion.li
                  key={item.name}
                  className="nav__item"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 + 0.3 }}
                >
                  <Link
                    to={item.href}
                    className={`nav__link ${location.pathname === item.href ? 'nav__link--active' : ''}`}
                    onClick={closeMenu}
                  >
                    {item.name}
                  </Link>
                </motion.li>
              ))}
            </ul>
          </nav>

          {/* Authentication Section */}
          <motion.div
            className="header__auth"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.8 }}
          >
            {isAuthenticated ? (
              <div className="user-menu">
                <button
                  className="user-menu__trigger"
                  onClick={toggleUserMenu}
                >
                  <div className="user-avatar">
                    {user?.name?.charAt(0).toUpperCase() || 'U'}
                  </div>
                  <span className="user-name">{user?.name}</span>
                  <svg
                    className={`user-menu__arrow ${showUserMenu ? 'user-menu__arrow--open' : ''}`}
                    width="12"
                    height="8"
                    viewBox="0 0 12 8"
                    fill="none"
                  >
                    <path d="M1 1L6 6L11 1" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>

                <AnimatePresence>
                  {showUserMenu && (
                    <motion.div
                      className="user-menu__dropdown"
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.2 }}
                    >
                      <div className="user-menu__header">
                        <div className="user-avatar user-avatar--large">
                          {user?.name?.charAt(0).toUpperCase() || 'U'}
                        </div>
                        <div className="user-info">
                          <div className="user-info__name">{user?.name}</div>
                          <div className="user-info__email">{user?.email}</div>
                          {isAdmin && <div className="user-info__role">Admin</div>}
                        </div>
                      </div>

                      <div className="user-menu__divider"></div>

                      <div className="user-menu__items">
                        {isAdmin && (
                          <Link
                            to="/admin/projects"
                            className="user-menu__item"
                            onClick={closeUserMenu}
                          >
                            <span className="user-menu__icon">🏗️</span>
                            Manage Projects
                          </Link>
                        )}
                        <Link
                          to="/profile"
                          className="user-menu__item"
                          onClick={closeUserMenu}
                        >
                          <span className="user-menu__icon">👤</span>
                          Profile
                        </Link>
                        <button
                          className="user-menu__item user-menu__item--logout"
                          onClick={handleLogout}
                        >
                          <span className="user-menu__icon">🚪</span>
                          Logout
                        </button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ) : (
              <div className="auth-buttons">
                <Link to="/login" className="btn btn-outline">
                  Login
                </Link>
                <Link to="/signup" className="btn btn-primary">
                  Sign Up
                </Link>
              </div>
            )}
          </motion.div>

          {/* Mobile Menu Button */}
          <motion.button
            className={`header__menu-btn ${isMenuOpen ? 'menu-btn--open' : ''}`}
            onClick={toggleMenu}
            whileTap={{ scale: 0.9 }}
          >
            <span></span>
            <span></span>
            <span></span>
          </motion.button>
        </div>

        {/* Mobile Navigation */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.div
              className="header__mobile-menu"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <nav className="mobile-nav">
                <ul className="mobile-nav__list">
                  {menuItems.map((item, index) => (
                    <motion.li
                      key={item.name}
                      className="mobile-nav__item"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Link
                        to={item.href}
                        className={`mobile-nav__link ${location.pathname === item.href ? 'mobile-nav__link--active' : ''}`}
                        onClick={closeMenu}
                      >
                        {item.name}
                      </Link>
                    </motion.li>
                  ))}
                </ul>
                <motion.div
                  className="mobile-nav__auth"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                >
                  {isAuthenticated ? (
                    <div className="mobile-user-info">
                      <div className="mobile-user-header">
                        <div className="user-avatar">
                          {user?.name?.charAt(0).toUpperCase() || 'U'}
                        </div>
                        <div className="mobile-user-details">
                          <div className="mobile-user-name">{user?.name}</div>
                          <div className="mobile-user-email">{user?.email}</div>
                          {isAdmin && <div className="mobile-user-role">Admin</div>}
                        </div>
                      </div>

                      <div className="mobile-user-actions">
                        {isAdmin && (
                          <Link
                            to="/admin/projects"
                            className="mobile-nav__link"
                            onClick={closeMenu}
                          >
                            Manage Projects
                          </Link>
                        )}
                        <Link
                          to="/profile"
                          className="mobile-nav__link"
                          onClick={closeMenu}
                        >
                          Profile
                        </Link>
                        <button
                          className="btn btn-outline mobile-logout-btn"
                          onClick={handleLogout}
                        >
                          Logout
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="mobile-auth-buttons">
                      <Link to="/login" className="btn btn-outline" onClick={closeMenu}>
                        Login
                      </Link>
                      <Link to="/signup" className="btn btn-primary" onClick={closeMenu}>
                        Sign Up
                      </Link>
                    </div>
                  )}
                </motion.div>
              </nav>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.header>
  );
};

export default Header;
