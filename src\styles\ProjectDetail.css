/* Project Detail Styles */
.project-detail {
  min-height: 100vh;
  padding-top: 80px;
}

/* Loading and Error States */
.project-detail-loading,
.project-detail-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: var(--space-8);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-primary);
  border-top: 4px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Hero Section */
.project-hero {
  background: var(--bg-secondary);
  padding: var(--space-12) 0;
  border-bottom: 1px solid var(--border-primary);
}

.project-hero__content {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--space-8);
  align-items: start;
}

.project-breadcrumb {
  margin-bottom: var(--space-4);
}

.breadcrumb-link {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: var(--text-sm);
  transition: var(--transition-normal);
}

.breadcrumb-link:hover {
  color: var(--accent-primary);
}

.project-title {
  font-size: var(--text-4xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  line-height: 1.2;
}

.project-location {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
}

.project-meta {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.project-status {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.project-status--planning {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.project-status--under-construction {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.project-status--completed {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.project-status--on-hold {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.project-status--upcoming {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

.project-featured {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
}

.project-price {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--accent-primary);
  margin-bottom: var(--space-4);
}

.project-progress {
  margin-bottom: var(--space-6);
}

.progress-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  border-radius: var(--radius-full);
  transition: width 0.6s ease;
}

.project-hero__actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  min-width: 200px;
}

/* Gallery Section */
.project-gallery {
  padding: var(--space-12) 0;
}

.project-gallery h2 {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-8);
  text-align: center;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-4);
}

.gallery-item {
  aspect-ratio: 16/10;
  border-radius: var(--radius-lg);
  overflow: hidden;
  cursor: pointer;
  transition: var(--transition-normal);
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-normal);
}

.gallery-item:hover img {
  transform: scale(1.05);
}

/* Project Details */
.project-details {
  padding: var(--space-12) 0;
  background: var(--bg-secondary);
}

.details-grid {
  display: grid;
  gap: var(--space-8);
}

.detail-section {
  background: var(--bg-primary);
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
}

.detail-section h3 {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.detail-section p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Specifications */
.specs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-3);
}

.spec-item {
  display: flex;
  justify-content: space-between;
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--border-primary);
}

.spec-label {
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: capitalize;
}

.spec-value {
  color: var(--text-primary);
  font-weight: 600;
}

/* Amenities */
.amenities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-2);
}

.amenity-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2);
}

.amenity-icon {
  color: var(--accent-primary);
  font-weight: 600;
}

.amenity-name {
  color: var(--text-primary);
}

/* Timeline */
.timeline {
  position: relative;
  padding-left: var(--space-6);
}

.timeline::before {
  content: '';
  position: absolute;
  left: 12px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--border-primary);
}

.timeline-item {
  position: relative;
  margin-bottom: var(--space-6);
}

.timeline-marker {
  position: absolute;
  left: -18px;
  top: 8px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--bg-tertiary);
  border: 2px solid var(--border-primary);
}

.timeline-item--completed .timeline-marker {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
}

.timeline-item--in-progress .timeline-marker {
  background: #3b82f6;
  border-color: #3b82f6;
}

.timeline-content h4 {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.timeline-content p {
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
}

.timeline-dates {
  display: flex;
  gap: var(--space-4);
  font-size: var(--text-sm);
  color: var(--text-tertiary);
}

/* Facilities */
.facilities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-3);
}

.facility-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
}

.facility-name {
  color: var(--text-primary);
  font-weight: 500;
}

.facility-distance {
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

/* Contact Section */
.project-contact {
  padding: var(--space-12) 0;
}

.contact-card {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  text-align: center;
  color: white;
}

.contact-card h3 {
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-bottom: var(--space-2);
}

.contact-card p {
  font-size: var(--text-lg);
  margin-bottom: var(--space-6);
  opacity: 0.9;
}

.contact-actions {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.contact-actions .btn {
  min-width: 180px;
}

/* Image Modal */
.image-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: var(--space-4);
}

.image-modal {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.image-modal img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
}

.image-modal-close {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: var(--text-xl);
  cursor: pointer;
  z-index: 1;
}

.image-modal-nav {
  position: absolute;
  bottom: var(--space-4);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  background: rgba(0, 0, 0, 0.7);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-full);
  color: white;
}

.image-modal-nav button {
  background: none;
  border: none;
  color: white;
  font-size: var(--text-lg);
  cursor: pointer;
  padding: var(--space-1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .project-hero__content {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .project-hero__actions {
    min-width: auto;
  }

  .project-title {
    font-size: var(--text-2xl);
  }

  .gallery-grid {
    grid-template-columns: 1fr;
  }

  .specs-grid,
  .amenities-grid,
  .facilities-grid {
    grid-template-columns: 1fr;
  }

  .contact-actions {
    flex-direction: column;
    align-items: center;
  }

  .timeline-dates {
    flex-direction: column;
    gap: var(--space-1);
  }
}

/* Additional responsive adjustments */
@media (max-width: 480px) {
  .project-detail {
    padding-top: 70px;
  }

  .project-hero {
    padding: var(--space-8) 0;
  }

  .project-gallery,
  .project-details,
  .project-contact {
    padding: var(--space-8) 0;
  }

  .detail-section {
    padding: var(--space-4);
  }

  .contact-card {
    padding: var(--space-6);
  }

  .image-modal-overlay {
    padding: var(--space-2);
  }
}
