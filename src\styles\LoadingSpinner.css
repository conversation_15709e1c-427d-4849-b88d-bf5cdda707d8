/* Loading Spinner Styles */
.loading-spinner {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-bg);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 3px solid var(--border-primary);
  border-top: 3px solid var(--accent-primary);
  border-radius: 50%;
  position: relative;
  margin-bottom: var(--space-4);
}

.spinner-inner {
  width: 40px;
  height: 40px;
  border: 2px solid transparent;
  border-top: 2px solid var(--accent-secondary);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: spin-reverse 0.8s linear infinite;
}

.loading-text {
  color: var(--text-secondary);
  font-size: var(--text-lg);
  font-weight: 500;
}

@keyframes spin-reverse {
  from {
    transform: translate(-50%, -50%) rotate(360deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(0deg);
  }
}
