/* About Page Styles */
.about-page {
  min-height: calc(100vh - 80px);
  background: var(--primary-bg);
}

/* About Hero Section */
.about-hero {
  padding: var(--space-24) 0 var(--space-16);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.about-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: var(--text-6xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: var(--text-xl);
  color: var(--text-secondary);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto var(--space-12);
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-8);
  max-width: 800px;
  margin: 0 auto;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--text-4xl);
  font-weight: 700;
  color: var(--accent-primary);
  margin-bottom: var(--space-2);
}

.stat-label {
  font-size: var(--text-base);
  color: var(--text-secondary);
  font-weight: 500;
}

/* Mission & Vision Section */
.mission-vision {
  padding: var(--space-20) 0;
  background: var(--secondary-bg);
}

.mission-vision-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-12);
  max-width: 1200px;
  margin: 0 auto;
}

.mission,
.vision {
  padding: var(--space-8);
  background: var(--glass-bg);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--border-primary);
}

.mission h2,
.vision h2 {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  position: relative;
}

.mission h2::after,
.vision h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--accent-gradient);
  border-radius: var(--radius-full);
}

.mission p,
.vision p {
  color: var(--text-secondary);
  line-height: 1.7;
  font-size: var(--text-lg);
}

/* Values Section */
.values-section {
  padding: var(--space-20) 0;
}

.section-title {
  font-size: var(--text-5xl);
  font-weight: 700;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: var(--space-16);
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-8);
}

.value-card {
  background: var(--secondary-bg);
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--border-primary);
  text-align: center;
  transition: var(--transition-normal);
}

.value-card:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-glow);
}

.value-icon {
  font-size: var(--text-6xl);
  margin-bottom: var(--space-4);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--accent-primary);
}

.value-icon-svg {
  width: 1em;
  height: 1em;
  filter: drop-shadow(0 0 8px rgba(99, 102, 241, 0.3));
  transition: transform 0.3s ease, filter 0.3s ease;
}

.value-card:hover .value-icon-svg {
  transform: scale(1.1) translateY(-5px);
  filter: drop-shadow(0 0 12px rgba(99, 102, 241, 0.5));
}

.value-title {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.value-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Team Section */
.team-section {
  padding: var(--space-20) 0;
  background: var(--secondary-bg);
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-8);
}

.team-card {
  background: var(--primary-bg);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  border: 1px solid var(--border-primary);
  transition: var(--transition-normal);
}

.team-card:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-glow);
}

.member-image {
  height: 300px;
  overflow: hidden;
}

.member-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-normal);
}

.team-card:hover .member-image img {
  transform: scale(1.05);
}

.member-info {
  padding: var(--space-6);
}

.member-name {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.member-position {
  color: var(--accent-primary);
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-1);
}

.member-experience {
  color: var(--text-muted);
  font-size: var(--text-sm);
  margin-bottom: var(--space-3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.member-bio {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Timeline Section */
.timeline-section {
  padding: var(--space-20) 0;
}

.timeline {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--accent-gradient);
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin-bottom: var(--space-12);
}

.timeline-item:nth-child(odd) .timeline-content {
  margin-right: 50%;
  padding-right: var(--space-8);
  text-align: right;
}

.timeline-item:nth-child(even) .timeline-content {
  margin-left: 50%;
  padding-left: var(--space-8);
}

.timeline-content {
  background: var(--secondary-bg);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-primary);
  position: relative;
}

.timeline-content::before {
  content: '';
  position: absolute;
  top: 50%;
  width: 20px;
  height: 20px;
  background: var(--accent-primary);
  border-radius: 50%;
  transform: translateY(-50%);
}

.timeline-item:nth-child(odd) .timeline-content::before {
  right: -10px;
}

.timeline-item:nth-child(even) .timeline-content::before {
  left: -10px;
}

.timeline-year {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--accent-primary);
  margin-bottom: var(--space-2);
}

.timeline-event {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.timeline-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* CTA Section */
.cta-section {
  padding: var(--space-20) 0;
  background: var(--secondary-bg);
  text-align: center;
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.cta-content h2 {
  font-size: var(--text-4xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.cta-content p {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-8);
}

.cta-button {
  padding: var(--space-4) var(--space-8);
  background: var(--accent-gradient);
  border: none;
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--text-lg);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
}

.cta-button:hover {
  background: var(--accent-gradient-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: var(--text-4xl);
  }
  
  .hero-subtitle {
    font-size: var(--text-lg);
  }
  
  .hero-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-4);
  }
  
  .mission-vision-content {
    grid-template-columns: 1fr;
    gap: var(--space-8);
  }
  
  .section-title {
    font-size: var(--text-3xl);
  }
  
  .values-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
  
  .team-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
  
  .timeline::before {
    left: var(--space-4);
  }
  
  .timeline-item:nth-child(odd) .timeline-content,
  .timeline-item:nth-child(even) .timeline-content {
    margin-left: var(--space-12);
    margin-right: 0;
    padding-left: var(--space-6);
    padding-right: var(--space-6);
    text-align: left;
  }
  
  .timeline-item:nth-child(odd) .timeline-content::before,
  .timeline-item:nth-child(even) .timeline-content::before {
    left: -10px;
    right: auto;
  }
}

@media (max-width: 480px) {
  .hero-stats {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
  
  .stat-number {
    font-size: var(--text-3xl);
  }
  
  .mission,
  .vision {
    padding: var(--space-6);
  }
  
  .value-card {
    padding: var(--space-6);
  }
  
  .cta-content h2 {
    font-size: var(--text-3xl);
  }
}
